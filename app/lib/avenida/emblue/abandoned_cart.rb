module Avenida
  module Emblue
    class AbandonedCart
      attr_reader :cart, :customer, :items

      def initialize(args = {})
        @cart = args[:cart]
        @customer = @cart.customer
        @items = @cart.items
      end

      def to_json
        {
          email: customer&.email,
          eventName: 'carrito_abandonado',
          attributes: {
            nombre: customer&.first_name,
            apellido: customer&.last_name,
            event_items: filtered_items&.map do |product|
              {
                nombre_producto: product.title,
                precio: product.regular_price.to_f,
                precio_oferta: product.price.to_f
              }
            end
          }
        }
      end

      private

      def filtered_items
        return [] unless items

        items.reject do |item|
          product = item.product
          product.points? && product.regular_price.to_f == 0 && product.price.to_f == 0
        end
      end
    end
  end
end
