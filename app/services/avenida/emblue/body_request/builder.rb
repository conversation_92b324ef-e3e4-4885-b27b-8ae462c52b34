module Avenida
  module Emblue
    module BodyRequest
      class Builder
        attr_reader :cart, :customer, :items

        def initialize(args = {})
          @cart = args[:cart]
          @customer = @cart.customer
          @items = @cart.items
        end

        def call
          result = build_payload
        rescue StandardError => e
          OpenStruct.new({success?: false, error: e})
        else
          OpenStruct.new({success?: true, payload: result})
        end

        def build_payload
          {
            email: customer.email,
            eventName: 'carrito_abandonado',
            attributes: {
              nombre: customer.first_name,
              apellido: customer.last_name,
              total_carrito: cart.total.to_i,
              event_items: filtered_items.map do |item|
                {
                  nombre_producto: item.title,
                  precio: item.product.regular_price.to_f,
                  precio_oferta: item.product.price.to_f,
                  url_producto: "#{cart.store.hostname}/products/#{item.product.slug}",
                  imagen: item.product.picture.url,
                  descripcion: item.product.description
                }
              end
            }
          }
        end

        private

        def filtered_items
          items.reject do |item|
            product = item.product
            product.points? && product.regular_price.to_f == 0 && product.price.to_f == 0
          end
        end
      end
    end
  end
end
