module Pioneer
  class OrderExporter < Reporting::Exporter
    attr_reader :network, :orders

    HEADER = {
      'AR' => [
        'Store',
        'Order Creation Date',
        'Payment Date',
        'Payment Status',
        'Shipment Status',
        'Order ID',
        'Order Total',
        'Suborder ID',
        'Sold By',
        'Fulfilled By',
        'Customer Name',
        'Customer Email',
        'Customer DNI',
        'Customer Address',
        'City',
        'Zip code',
        'Product Title',
        'Product Variant',
        'GP SKU',
        'External SKU',
        'Variant Cost',
        'Category Path',
        'Quantity',
        'Product Price',
        'Sale Price',
        'Shop Coupon',
        'Network Coupon',
        'Coupon Discount',
        'Promotion Applied',
        'Promotion Discount',
        'Paid Shipping Price',
        'Iva',
        'Subsidized Shipping Price',
        'Shipping Address Province',
        'Courier',
        'Tracking Number',
        'Tel.',
        'Label Amount',
        'Label Paid by',
        'Avenida Commission',
        'Commission Percentage',
        'Full Name for Shipment',
        'Gateway',
        'Bank',
        'Payment External Id',
        'Payment Method',
        'ID Redención',
        'Payment Total',
        'CreditCard',
        'Bin',
        'Installments',
        'Label Date',
        'Invoice Id',
        'Credit Note Ids',
        'ID Cobis',
        'Total de puntos consumidos',
        'Relación Puntos de la transacción',
        'TIPO_DOC_PAGO',
        'DOC_PAGO',
        'NOMBRE_Y_APELLIDO_TARJETA',
        'NUMEROS_TARJETA',
        'IP'
      ],
      'US' => [
        'Order Creation Date',
        'Payment Status',
        'Shipment Status',
        'Order ID',
        'Order Total',
        'Suborder ID',
        'Sold By',
        'User Name',
        'User Email',
        'Customer Address',
        'City',
        'Zip code',
        'Product Title',
        'Product Variant',
        'External SKU',
        'Category Path',
        'Quantity',
        'Product Price',
        'Sale Price',
        'Shop Coupon',
        'Network Coupon',
        'Coupon Discount',
        'Promotion Applied',
        'Promotion Discount',
        'Sales Tax',
        'Paid Shipping Price',
        'Subsidized Shipping Price',
        'Label Paid by',
        'Avenida Commission',
        'Commission Percentage',
        'Full Name for Shipment',
        'IP'
      ]
    }

    def initialize(options = {})
      @network = options[:network]
      @orders = options[:orders]
      @time_zone = Network[@network].time_zone || 'UTC'
    end

    def filename
      "#{@network.downcase}_orders_#{Time.current.strftime('%Y-%m-%d_%H%M%S')}"
    end

    def stream
      Enumerator.new do |result|
        result << csv_header

        yielder do |row|
          result << csv_row(row)
        end
      end
    end

    private

    def csv_header
      CSV::Row.new(HEADER[network], HEADER[network], true).to_s
    end

    def csv_row(values)
      CSV::Row.new(HEADER[network], values).to_s
    end

    def yielder
      Time.use_zone(@time_zone) do
        orders.each do |order|
          if order.class.name == 'SaleItem' || order.class.name == 'Purchase'
            if order.listable_type == 'Purchase'
              yield row_data_purchases(order)
            else
              order.suborders.each do |suborder|
                suborder.items.each do |item|
                  yield row_data(item, suborder, order)
                end
              end
            end
          else
            order.suborders.each do |suborder|
              suborder.items.each do |item|
                yield row_data(item, suborder, order)
              end
            end
          end
        end
      end
    end

    def row_data(item, suborder, order)
      payer_identification = suborder.payment.gateway_data[:payer][:identification]

      [
        store_name(order),
        creation_date(order),
        payment_date(order),
        payment_status(order),
        shipment_status(suborder.shipment),
        order_id(order),
        total(order),
        suborder.public_id,
        sold_by(suborder),
        (fullfilled_by(suborder) if network == 'AR'),
        customer_name(order),
        customer_email(order),
        (customer_dni(order) if network == 'AR'),
        customer_address(suborder.shipment),
        customer_city(suborder.shipment),
        zip_code(suborder.shipment),
        product_title(item),
        product_variant(item),
        (gp_sku(item) if network == 'AR'),
        external_sku(item),
        get_variant_cost(item, order),
        category_path(item),
        product_quantity(item),
        product_price(item),
        product_sale_price(item),
        shop_coupon(suborder),
        network_coupon(order),
        coupon_discount(order, item),
        promotion_applied(order),
        promotion_discount(order, item),
        (sale_tax(suborder) if network == 'US'),
        paid_shipping_price(item),
        iva(item.product),
        subsidized_shipping_price(suborder, item, order),
        (shipping_address_province(suborder) if network == 'AR'),
        (label_courier(suborder) if network == 'AR'),
        (label_tracking_number(suborder.shipments.last) if network == 'AR'),
        (get_tel(suborder.shipments.last) if network == 'AR'),
        (label_amount(suborder, item) if network == 'AR'),
        label_paid_by(suborder, network),
        goodpeople_commission(item),
        shop_commission(suborder),
        destination_address(suborder),
        gateway(order),
        get_cc_bank(order),
        suborder_payment_external_id(suborder),
        suborder_payment_method(suborder),
        get_purchase_id(suborder),
        suborder_payment_total(suborder),
        get_cc_brand(order),
        get_cc_bin(order),
        get_installments(order),
        get_label_date(suborder.shipment),
        get_invoice_id(suborder),
        get_credit_notes_ids(order.credit_notes),
        get_id_cobis(order.customer),
        get_total_consumed_points(order),
        get_points_relation(item),
        payer_identification[:type] || '-',
        payer_identification[:number] || '-',
        payer_identification[:name_payment] || '-',
        payer_identification[:card_last_four_digits] || '-',
        order.ip
      ].compact
    end

    def row_data_purchases(order)
      [
        store_name(order),
        creation_date(order),
        payment_date(order),
        payment_status(order),
        ' - ',
        order_id(order),
        total(order),
        ' - ',
        order.store.title,
        ' - ',
        customer_name(order),
        customer_email(order),
        (customer_dni(order) if network == 'AR'),
        ' - ',
        ' - ',
        ' - ',
        order.title,
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        '1',
        order.total,
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        gateway(order),
        ' - ',
        get_payment_external_id(order),
        payment_method(order),
        get_purchase_id_for_purchase(order),
        payment_total(order),
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        ' - ',
        get_id_cobis(order.customer),
        get_total_consumed_points(order),
        get_points_relation(order)
      ].compact
    end

    # Extracting methods.
    def order_id(order)
      order.class.name == 'SaleItem' ? order.listable.id : order.id
    end

    def store_name(order)
      order.store.name
    end

    def creation_date(order)
      order.created_at
    end

    def payment_date(order)
      if order.payment && order.payment.collected_at
        order.payment.collected_at
      else
        ' - '
      end
    end

    def payment_status(order)
      payment = order.payment

      if payment.present?
        payment.status
      else
        if order.balance_due?
          'parcialmente pagado (revisar)'
        else
          'totalmente pagado con cupón'
        end
      end
    end

    def shipment_status(shipment)
      return ' - ' if shipment.nil?
      shipment&.status || ' - '
    end

    def total(order)
      order.total.to_f
    end

    def sold_by(suborder)
      suborder.shop.title
    end

    def fullfilled_by(suborder)
      return Mkp::Shop.find_by_id(Network[network].default_shop_id).title if suborder.fulfilled_by_gp?
      ' - '
    rescue
      ' - '
    end

    def customer_name(order)
      order.customer.try(:full_name) || ' - '
    end

    def customer_email(order)
      order.customer.try(:email) || ' - '
    end

    def customer_dni(order)
      customer_last_dni_stored = order.customer.addresses.map(&:doc_number).uniq.compact.last
      return customer_last_dni_stored if customer_last_dni_stored.present?
      if (payment = order.payment).present?
        if payment.gateway == "Empty" && payment.gateway_data.dig(:cuil).present?
           payment_stored_dni = payment.gateway_data.dig(:cuil).slice(2..-3)
        else
          payment_stored_dni = if (payment.gateway_data.kind_of?(Hash) && data = payment.gateway_data.deep_symbolize_keys)
            data[:payer][:identification][:number] ||
            ( data[:cardholder] && data[:cardholder][:identification][:number] )
          end
        end
        return payment_stored_dni if payment_stored_dni.present?
      end

      ' - '
    rescue NoMethodError => e
      ' - '
    end

    def destination_address(suborder)
      return ' - ' if suborder.shipment.nil?
      suborder.shipment.destination_address.full_name rescue ' - '
    end

    def product_title(order_item)
      order_item.variant.product.try(:title) || ' - '
    end

    def product_variant(order_item)
      order_item.variant.try(:name) || ' - '
    end

    def gp_sku(order_item)
      gp_sku = order_item.variant.try(:gp_sku)
      return ' - ' unless gp_sku.present?
      gp_sku.strip
    end

    def external_sku(order_item)
      sku = order_item.variant.try(:sku)
      return ' - ' unless sku.present?
      sku.strip
    end

    def category_path(order_item)
      category =  order_item.variant.product.category
      return category.full_path if category.present?
      " - "
    rescue NoMethodError => e
      " - "
    end

    def product_quantity(order_item)
      order_item.quantity
    end

    def product_price(order_item)
      order_item.price.to_f
    end

    def product_sale_price(order_item)
      order_item.sale_price.to_f
    end

    def shop_coupon(suborder)
      suborder.coupon.try(:code) || ' - '
    end

    def network_coupon(order)
      order.coupon.try(:code) || ' - '
    end

    def coupon_discount(order, item)
      return 0.0 unless (discount = order.coupon_discount.to_f) > 0
      total_products = order.total_products
      div = total_products > 0 ? discount / order.total_products : discount
      (div * item.quantity).round(2)
    end

    def paid_shipping_price(item)
      item.shipment_cost
    end

    def iva(product)
      product.iva || ' - '
    end

    def subsidized_shipping_price(suborder, item, order)
      # cuando es fulfilled por GP, hay mas de un shipment en la orden, y paid shipping mayor a $0
      if suborder.fulfilled_by_gp && order.shipments.count > 1 && order.shipments_cost > 0
        return (suborder.shipment_cost.to_f / suborder.order.total_products) * item.quantity
      end
      return 0.0 unless suborder.has_shipment_bonification?
      return 0.0 if suborder.shipment.nil?
      (suborder.shipment.bonified_amount.to_f / suborder.total_products) * item.quantity rescue 0.0
    end

    def shipping_address_province(suborder)
      suborder.shipment.destination_address.state rescue ' - '
    end

    def goodpeople_commission(order_item)
      order_item.commission.to_f rescue 0.0
    end

    def shop_commission(suborder)
      suborder.shop.setting.commercial_agreement[:sale_commission].to_f
    end

    def sale_tax(suborder)
      suborder.taxes.to_f || 0.0
    end

    def label_courier(suborder)
      return ' - ' if suborder.shipments.blank?
      shipment = suborder.shipments.last
      if shipment.label.present?
        shipment.label[:courier]
      else
        ' - '
      end
    end

    def label_tracking_number(shipment)
      return ' - ' if shipment.nil?
      return ' - ' unless shipment.label.present?
      shipment.label[:tracking_number]
    end

    def get_tel(shipment)
      return ' - ' unless shipment.present? && shipment.destination_address.present?
      shipment.destination_address.telephone
    end

    def label_amount(suborder, item)
      return ' - ' if suborder.shipments.blank?
      shipment = suborder.shipments.last
      if shipment.label.present? && shipment.label[:gateway_data].present? && \
      shipment.label[:gateway_data][:rate].present?
        (shipment.label[:gateway_data][:rate][:precio].to_f / suborder.total_products) * item.quantity
      else
        " - "
      end
    end

    def label_paid_by(suborder, network)
      return ' - ' if suborder.shipments.blank?
      shipment = suborder.shipments.last
      if shipment.status == 'unfulfilled'
        ' - '
      elsif (shipment.label.present? && shipment.label[:gateway].present?) \
            || (network == 'AR' && shipment[:gateway].present?)
        'Avenida Store'
      else
        'Shop'
      end
    end

    def promotion_applied(order)
      if (promotion = order.data[:promotion]).present?
        promotion[:display_name]
      else
        ' - '
      end
    end

    def promotion_discount(order, item)
      return 0.0 unless (promotion = order.data[:promotion]).present?
      (promotion[:discount].to_f / order.total_products) * item.quantity
    end

    def gateway(order)
      return " - " unless order.payments.any?
      order.payments.map(&:gateway).join('|')
    end

    def get_cc_bank(order)
      return " - " unless order.payments.any?
      order.payments.map{|p| p.get_cc_bank || ' - '}.join(' | ')
    end

    def payment_method(order)
      return " - " unless order.payments.any?
      order.payments.map{|p| p.payment_method || ' - '}.join(' | ')
    end

    def payment_total(order)
      return " - " unless order.payments.any?
      order.payments.map{|p| p.collected_amount.to_i}.join(' | ')
    end

    def get_cc_brand(order)
      return " - " unless order.payments.any?
      order.payments.map{|p| p.get_cc_brand || ' - '}.join(' | ')
    end

    def get_cc_bin(order)
      return " - " unless order.payments.any?
      order.payments.map{|p| p.get_cc_bin || ' - '}.join(' | ')
    end

    def get_installments(order)
      return " - " unless order.payments.any?
      order.payments.map{|p| p.get_installments || ' - '}.join(' | ')
    end

    def get_payment_external_id(order)
      return " - " unless order.payments.any?
      order.payments.map{|p| p.gateway_object_id || ' - '}.join(' | ')
    end

    def get_label_date(shipment)
      if shipment.present? && shipment.label.present?
        return shipment.label.created_at
      else
        " - "
      end
    end

    def get_invoice_id(suborder)
      if (invoices = suborder.order.invoices).present? && !suborder.shop.fc?
        invoices.first.gateway_data[:number]
      else
        " - "
      end
    end

    def get_credit_notes_ids(credit_notes)
      return ' - ' unless credit_notes.present?
      credit_notes.map{|credit_note| credit_note.number}.join(' | ')
    end

    def zip_code(shipment)
      return ' - ' if shipment.nil?
      shipment.destination_address[:zip]
    end

    def customer_address(shipment)
      return ' - ' if shipment.nil?
      destination_address = shipment.destination_address[:address]
      if shipment.destination_address[:address_2].present?
        destination_address = destination_address + " - " + shipment.destination_address[:address_2]
      end
      destination_address
    end

    def customer_city(shipment)
      return ' - ' if shipment.nil?
      shipment.destination_address[:city]
    end

    def get_variant_cost(item, order)
      cost = item.variant.current_cost(order.created_at)
      return cost unless cost.nil?
      " - "
    end

    def get_id_cobis(customer)
      customer.try(:uuid) || ' - '
    end

    def get_total_consumed_points(order)
      order.try(:total_points) || ' - '
    end

    def get_points_relation(item)
      item.try(:point_equivalent) || '-'
    end

    def suborder_payment_total(suborder)
      suborder.total_without_points
    end

    def suborder_payment_method(suborder)
      return " - " unless order.payments.any? || suborder.payments.any?
      (suborder.payments + suborder.order.payments.where.not(gateway: "VisaPuntos")).map{|p| p.payment_method_name}.join(' | ')
    end

    def suborder_payment_external_id(suborder)
      return " - " unless order.payments.any? || suborder.payments.any?
      (suborder.payments + suborder.order.payments.where.not(gateway: "VisaPuntos")).map{|p| p.gateway_object_id}.join(' | ')
    end

    def get_purchase_id(suborder)
      # Solo para órdenes con pagos de puntos (LoyaltyBna)
      points_payment = (suborder.payments + suborder.order.payments).find { |p| p.gateway == "LoyaltyBna" }
      return "-" unless points_payment.present?

      purchase_id = points_payment.get_subpayment_site_id(suborder, suborder.shop)&.dig('points_uuid')
      return "-" unless purchase_id.present?

      # Formatear como en la vista: quitar guiones y tomar primeros 20 caracteres
      purchase_id.to_s.gsub('-', '')[0..19]
    end

    def get_purchase_id_for_purchase(order)
      # Para purchases, verificar si hay pagos de puntos
      points_payment = order.payments.find { |p| p.gateway == "LoyaltyBna" }
      return "-" unless points_payment.present?

      # Para purchases, el purchase_id puede estar directamente en gateway_data
      purchase_id = points_payment.gateway_data&.dig('points_uuid')
      return "-" unless purchase_id.present?

      # Formatear como en la vista: quitar guiones y tomar primeros 20 caracteres
      purchase_id.to_s.gsub('-', '')[0..19]
    end
  end
end
