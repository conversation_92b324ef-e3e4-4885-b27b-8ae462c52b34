class ExportSalesWorker # rubocop:disable Metrics/ClassLength
  include Sidekiq::Worker
  sidekiq_options queue: :critical, retry: true, unique: :while_executing_reschedule

  MAPPED_GATEWAY_STORES = {
    'decidir' => %w[Decidir AvenidaDecidir],
    'decidir_distributed' => %w[AvenidaDecidirDistributed DecidirDistributed DecidirDistributedMacro],
    'modo_distributed' => %w[AvenidaModoDistributed],
    'empty' => %w[Empty],
    'firstdata' => %w[FirstData],
    'firstdata_distributed' => %w[FirstDataDistributed],
    'mercadopago' => %w[Mercadopago],
    'mercadopago_ticket' => %w[Mercadopago],
    'system_points' => %w[SystemPoint],
    'tarjeta_digital' => %w[Tarjetadigital],
    'todopago' => %w[Todopago],
    'visa_puntos' => %w[VisaPuntos],
    'bogus' => %w[Bogus]
  }.freeze

  def perform (params)
    do_export(params)
  rescue StandardError => e
    Rails.logger.info("Error desconocido al exportar ordenes Pioneer: #{e}")
    Rails.logger.info("Params: #{params}")
  end

  def do_export(params)
    @date_from = params['order_created_from']
    @date_to = params['order_created_to']
    @store = params['store']
    current_file = filename(@date_from, @date_to)
    path_filename = path_file(current_file)
    orders = get_orders_info(@date_from, @date_to, @store).uniq
    data = build_csv(orders, params)
    csv = create_file(path_filename, data)
    file = StringIO.new(csv)
    update_export(params['export_id'], file, current_file) if params['export_id'].present?
  end

  def update_export(export_id, file, current_file)
    export = Mkp::Export.find_by(id: export_id)
    export.csv_file = file
    export.title = current_file
    export.csv_file.instance_write(:content_type, 'text/csv')
    export.csv_file.instance_write(:file_name, current_file)
    export.url = url_path(current_file)
    export.save!
  end

  def get_orders_info(date_from, date_to, store)
    from = Date.parse(date_from).beginning_of_day
    to = Date.parse(date_to).end_of_day
    Mkp::Order
      .includes(:store)
      .includes(customer: [:addresses])
      .includes(suborders: [{ shop: [:setting] },
                            :shipments,
                            :payments,
                            { items: [{ product: [:category, shop: [:setting]] },
                                      { variant: [product: [:shop, :category]] },
                                      :payments] }])
      .with_payment_status_not_pending_or_expired
      .where(created_at: from..to, store_id: store)
      .includes(shipments: [:labels])
      .includes(:invoices)
      .to_a
  end

  private

  def add_date_to_file(date_from, date_to, security = nil)
    from = Date.parse(date_from).strftime('%d-%m-%Y')
    to = Date.parse(date_to).strftime('%d-%m-%Y')
    "sales-#{security}-#{from}_al_#{to}.csv"
  end

  def filename(date_from, date_to)
    return @filename ||= add_date_to_file(date_from, date_to, security) unless date_from.nil?

    @filename ||= "sales-#{security}-#{date_format(Time.zone.now)}.csv"
  end

  def path_file(current_file)
    @path_file ||= "#{Rails.root}/public/#{current_file}"
  end

  def security
    SecureRandom.hex(8)
  end

  def url_path(current_filename)
    "#{HOSTNAME}/#{current_filename}"
  end

  def build_csv(orders, params)
    @row_data = []
    @payment_methods = orders&.map(&:payments)&.flatten&.map(&:gateway)&.compact&.uniq || []
    broken_orders = []

    CSV.generate do |csv|
      # Crear headers con todos los campos necesarios
      sample_order = orders.first
      if sample_order&.suborders&.first&.items&.first
        sample_suborder = sample_order.suborders.first
        sample_item = sample_suborder.items.first
        column_headers = build_payments_data(sample_order, sample_suborder, sample_item).keys
      else
        # Fallback si no hay órdenes
        column_headers = []
      end
      
      csv << column_headers
      orders.each do |order|
        order.suborders.each do |suborder|
          suborder.items.each do |item|
            begin
              data = build_payments_data(order, suborder, item)
              @row_data << data
            rescue
              broken_orders << order.id
              next
            end
          end
        end
      end

      @row_data.each do |data|
        csv << data.values
      end

      if !broken_orders.blank?
        message = "[EXPORTACION - ORDENES] N° #{params['export_id']}, correspondiente a la tienda #{Mkp::Order.find(broken_orders.last).store.title},
          no podra exportar las siguientes ordenes, ya que las mismas se encuentran dañadas:
          #{broken_orders}"
        RocketChatNotifier.notify message, webhook_dest: :platform
      end
    end
  end

  def build_payments_data(order, suborder = nil, item = nil)
    row_data = row_data_orders(order, suborder, item)
    data = get_payments(order, suborder, item)
    row_data.merge!(data)

  end

  def get_payments(order, suborder, item, fields_order = nil)
    result = {}
    payment_methods = order&.payments&.map(&:gateway)&.compact&.uniq || []
    payment_methods.each do |gateway|
      payment = find_payment(order, suborder, item, gateway)

      payment_data = {
        "AvenidaDistributed" => suborder.nil? ? get_payment_external_id(payment) : item_payment_external_ids(payment),
        "Medio de pago" => suborder.nil? ? payment_method(payment) : item_payment_methods(payment),
        "ID Redención" => get_purchase_id_for_export(payment, suborder),
        "Pago total" => suborder.nil? ? payment_total(payment) : suborder_payment_total(suborder, payment&.payment_method),
        "Cuotas" => get_installments(payment),
        "Tarjeta" => get_credit_card(payment),
        "Bin" => get_cc_number(payment),
        "NUMEROS_TARJETA" => card_last_four_digits(payment),
        "NOMBRE_Y_APELLIDO_TARJETA" => get_identification(payment, :name_payment),
        "TIPO_DOC_PAGO" => get_identification(payment, :type),
        "DOC_PAGO" => get_identification(payment, :number),
        "Banco" => item_cc_bank(payment)
      }

      payment_data = payment_data.slice(*fields_order) if fields_order
      result.merge!(payment_data)
    end
    result
  end

  def find_payment(order, suborder, item, gateway)
    payment = item&.payments&.find { |p| p.gateway == gateway }
    payment ||= order&.payments&.find { |p| p.gateway == gateway }
    payment
  end

  def row_data_orders(order, suborder = nil, item = nil)
    payment_fields_order = [
      "AvenidaDistributed",
      "Medio de pago",
      "ID Redención",
      "Pago total",
      "Cuotas",
      "Tarjeta",
      "Bin",
      "NUMEROS_TARJETA",
      "NOMBRE_Y_APELLIDO_TARJETA",
      "TIPO_DOC_PAGO",
      "DOC_PAGO",
      "Banco"
    ]

    payment_data = get_payments(order, suborder, item, payment_fields_order)

    {
      'Tienda' => order.blank? ? '-' : get_store_name(order),
      'Fecha creación orden' => order.blank? ? '-' : get_creation_date(order),
      'ID orden' => order.blank? ? '-' : get_order_id(order),
      'ID suborden' => type_row?(suborder, item) ? '-' : suborder.public_id,
      'Estado pago' => order.blank? ? '-' : get_payment_status(order),
      'Vendido por' => type_row?(suborder, item) ? order&.store&.title || '-' : get_sold_by(suborder),
      'Nombre cliente' => order.blank? ? '-' : get_customer_name(order),
      'Email cliente' => order.blank? ? '-' : get_customer_email(order),
      'DNI cliente' => order.blank? ? '-' : get_customer_dni(order),
      'Tel cliente' => type_row?(suborder, item) ? '-' : get_telephone(suborder, order),
      'Dirección envio' => type_row?(suborder, item) ? '-' : get_customer_address(suborder),
      'Ciudad envio' => type_row?(suborder, item) ? '-' : get_customer_city(suborder),
      'Provincia envio' => type_row?(suborder, item) ? '-' : get_customer_state(suborder),
      'Código postal envio' => type_row?(suborder, item) ? '-' : get_zip_code(suborder),
      'Recibe envio' => type_row?(suborder, item) ? '-' : get_destination_address_fullname(suborder),
      'Direccion facturacion' => type_row?(suborder, item) ? '-' : get_billing_address_address(suborder),
      'Ciudad facturacion' => type_row?(suborder, item) ? '-' : get_billing_address_city(suborder),
      'Provincia facturacion' => type_row?(suborder, item) ? '-' : get_billing_address_state(suborder),
      'Codigo postal facturacion' => type_row?(suborder, item) ? '-' : get_billing_address_zip_code(suborder),
      'Título producto' => type_row?(suborder, item) ? order&.title : sanitize(get_product_title(item).gsub(/[\r\n]?/, '')),
      'Category Path' => type_row?(suborder, item) ? '-' : get_category_path(item),
      'Iva' => type_row?(suborder, item) ? '-' : get_iva(item.product),
      'Cantidad' => type_row?(suborder, item) ? '1' : get_product_quantity(item),
      'Precio Regular' => type_row?(suborder, item) ? order&.total || '-' : get_product_regular_price(item),
      'Precio oferta' => type_row?(suborder, item) ? '-' : get_product_sale_price(item),
      'Precio de Producto' => type_row?(suborder, item) ? '-' : get_product_price(item),
      'PxQ' => type_row?(suborder, item) ? '-' : get_product_price(item) * get_product_quantity(item),
      'Precio envío pago' => type_row?(suborder, item) ? '-' : get_paid_shipping_price(item),
      'Costo financiero' => type_row?(suborder, item) ? '-' : (suborder.all_payments.sum(0.0) {|p| p.amount_coef(suborder)}).to_f.round(2),
      'Descuento cupon' => type_row?(suborder, item) ? '-' : get_item_coupon_discount(item),
      'Cupon proveedor' => type_row?(suborder, item) ? '-' : get_shop_coupon(suborder),
      'Total puntos gastados' => type_row?(suborder, item) ? get_total_consumed_points(order) : item_total_consumed_points(item),
      'Equivalencia puntos' => type_row?(suborder, item) ? get_points_money_relation(order) : get_points_money_relation(item),
      'Medio de pago' => payment_data["Medio de pago"],
      'Pago total' => payment_data["Pago total"],
      'Cuotas' => payment_data["Cuotas"],
      'Tarjeta' => payment_data["Tarjeta"],
      'Bin' => payment_data["Bin"],
      'NUMEROS_TARJETA' => payment_data["NUMEROS_TARJETA"],
      'NOMBRE_Y_APELLIDO_TARJETA' => payment_data["NOMBRE_Y_APELLIDO_TARJETA"],
      'TIPO_DOC_PAGO' => payment_data["TIPO_DOC_PAGO"],
      'DOC_PAGO' => payment_data["DOC_PAGO"],
      'Banco' => payment_data["Banco"],
      'AvenidaDistributed' => payment_data["AvenidaDistributed"],
      'Orden Total' => order.blank? ? '-' : get_order_total(order),
      'Fecha pago' => order.blank? ? '-' : get_payment_date(order),
      'Fecha actualización envío' => type_row?(suborder, item) ? '-' : get_shipment_status_updated_at(suborder),
      'Tipo de envio' => type_row?(suborder, item) ? 'Normal' : get_shipment_kind(suborder),
      'Mensajero' => type_row?(suborder, item) ? '-' : get_label_courier(suborder),
      'Número de seguimiento' => type_row?(suborder, item) ? '-' : get_label_tracking_number(suborder),
      'Estado envío' => type_row?(suborder, item) ? '-' : get_shipment_status(suborder),
      'Variante producto' => type_row?(suborder, item) ? '-' : get_product_variant(item),
      'GP SKU' => type_row?(suborder, item) ? '-' : get_gp_sku(item),
      'SKU externo' => type_row?(suborder, item) ? '-' : get_external_sku(item),
      'Comisión avenida' => type_row?(suborder, item) ? '-' : get_goodpeople_commission(item),
      'Comisión porcentaje' => type_row?(suborder, item) ? '-' : get_shop_commission(suborder),
      'IP' => type_row?(suborder, item) ? '-' : order.ip,
      'Motivo de cancelación' => get_cancelation_reason(suborder),
      'Comentario adicional' => get_order_comment(order)
    }
  end

  def create_file(filename, data)
    CSV.open(filename, 'wb') do |csv|
      data.each_line do |line|
        split_line = line.split(',')
        csv << split_line
      end
    end
  end

  def type_row?(suborder, item)
    suborder.nil? && item.nil?
  end

  def date_format(date)
    date&.strftime('%d-%m-%Y %H:%M:%S')
  end

  def sanitize(field)
    field.gsub(/[^A-Za-z0-9_.\- Á-Úá-úñÑ]/, '') if field.present?
  end

  def get_billing_address_address(suborder)
    return '-' if suborder.shipment.nil?
    sanitize(suborder.shipment.billing_address[:address])
  end

  def get_billing_address_city(suborder)
    return '-' if suborder.shipment.nil?
    sanitize(suborder.shipment.billing_address[:city])
  end

  def get_billing_address_zip_code(suborder)
    return '-' if suborder.shipment.nil?
    sanitize(suborder.shipment.billing_address[:postal_code])
  end

  def get_billing_address_state(suborder)
    return '-' if suborder.shipment.nil?
    sanitize(suborder.shipment.billing_address[:state])
  end

  def get_store_name(order)
    order.store.name
  end

  def get_creation_date(order)
    date_format(order.created_at)
  end

  def get_payment_date(order)
    date_format(order.payment&.collected_at) || '-'
  end

  def get_payment_status(order)
    payments = order.payments

    if payments.present?
      if payments.any? { |p| p.status == 'expired' }
        'Expirado'
      else
        payment = payments.first
        payment&.spanish_status || 'Estado desconocido'
      end
    elsif order.balance_due?
      'parcialmente pagado (revisar)'
    else
      'totalmente pagado con cupón'
    end
  end

  def get_shipment_status(suborder)
    I18n.t("pioneer.orders.#{suborder&.status}", default: 'Traduccion no encontrada')
  end

  def get_shipment_kind(suborder)
    shipment_kind = suborder.shipment.shipment_kind
    return 'Normal' if shipment_kind.blank? || shipment_kind == 'delivery'

    I18n.t("pioneer.orders.#{shipment_kind}", default: 'Pick up')
  end

  def get_shipment_status_updated_at(suborder)
    suborder.shipments.any? ? date_format(suborder.shipment.updated_at) : date_format(suborder.updated_at) # rubocop:disable Layout/LineLength
  end

  def get_order_id(order)
    order.id
  end

  def get_order_total(order)
    order.total.to_f
  end

  def get_sold_by(suborder)
    return suborder.shop.title if suborder.shop.present?

    suborder.store
  end

  def get_fullfilled_by(suborder)
    return 'Avenida' if suborder.fulfilled_by_gp?

    '-'
  end

  def get_customer_name(order)
    sanitize(order.customer.try(:full_name)) || '-'
  end

  def get_customer_email(order)
    order.customer.try(:email) || '-'
  end

  def get_customer_dni(order) # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/MethodLength, Metrics/PerceivedComplexity
    customer_last_dni_stored = order.customer.addresses.map(&:doc_number).uniq.compact.last
    return customer_last_dni_stored if customer_last_dni_stored.present?

    if (payment = order.payment).present?
      if payment.gateway == 'Empty' && payment.gateway_data.dig(:cuil).present?
        payment_stored_dni = payment.gateway_data.dig(:cuil).slice(2..-3)
      else
        payment_stored_dni = if payment.gateway_data.is_a?(Hash) && data = payment.gateway_data.deep_symbolize_keys # rubocop:disable Lint/AssignmentInCondition, Layout/LineLength
                               data[:payer][:identification][:number] ||
                                 (data[:cardholder] && data[:cardholder][:identification][:number])
                             end
      end
      return payment_stored_dni if payment_stored_dni.present?
    end

    order.customer.doc_number || '-'
  rescue StandardError
    '-'
  end

  def get_customer_address(suborder)
    return '-' if suborder.shipment.nil?

    shipment = suborder.shipment
    destination_address = sanitize(shipment.destination_address[:address])
    if shipment.destination_address[:address_2].present?
      destination_address = "#{destination_address} - #{sanitize(shipment.destination_address[:address_2])}" # rubocop:disable Layout/LineLength
    end
    destination_address
  end

  def get_zip_code(suborder)
    return '-' if suborder.shipment.nil?

    suborder.shipment.destination_address[:zip]
  end

  def get_customer_city(suborder)
    return '-' if suborder.shipment.nil?

    sanitize(suborder.shipment.destination_address[:city])
  end

  def get_customer_state(suborder)
    return '-' if suborder.shipment.nil?

    sanitize(suborder.shipment.destination_address[:state])
  end

  def get_product_title(order_item)
    order_item.reload if order_item.variant.blank?
    title = order_item.variant.product.try(:title)
    return sanitize(title.gsub(/[\r\n]?/, '')) if title.present?

    '-'
  end

  def get_product_variant(order_item)
    variant = order_item.variant.try(:name)
    return sanitize(variant.gsub(/[\r\n]?/, '')) if variant.present? && variant != 'true'

    '-'
  end

  def get_gp_sku(order_item)
    gp_sku = order_item.variant.try(:gp_sku)
    return '-' if gp_sku.blank?

    gp_sku.strip
  end

  def get_external_sku(order_item)
    sku = order_item.variant.try(:sku)
    return '-' if sku.blank?

    sku.strip
  end

  def get_variant_cost(item, order)
    cost = item.variant.current_cost(order.created_at)
    return cost unless cost.nil?

    '-'
  end

  def get_category_path(order_item)
    category =  order_item.variant.product.category
    return category.full_path if category.present?

    '-'
  rescue NoMethodError
    '-'
  end

  def get_product_quantity(order_item)
    order_item.quantity
  end

  def get_product_regular_price(order_item)
    order_item.price.to_f
  end

  def get_product_sale_price(order_item)
    order_item.sale_price.to_f
  end

  def get_product_price(order_item)
    sale_price = get_product_sale_price(order_item)
    sale_price.positive? ? sale_price : get_product_regular_price(order_item)
  end

  def get_shop_coupon(suborder)
    suborder.coupon.try(:code) || '-'
  end

  def get_network_coupon(order)
    order.coupon.try(:code) || '-'
  end

  def get_coupon_discount(order, item)
    return 0.0 unless (discount = order.coupon_discount.to_f).positive?

    total_products = order.total_products
    div = total_products.positive? ? discount / order.total_products : discount
    (div * item.quantity).round(2)
  end

  def get_item_coupon_discount(item)
    suborder = Mkp::OrderItem.where(id: item.id).first&.suborder
    discount = suborder.coupon_discount if suborder.present?
    discount.present? && discount.positive? ? discount.to_f : 0.0
  end

  def get_promotion_applied(order)
    if (promotion = order.data[:promotion]).present?
      promotion[:display_name]
    else
      '-'
    end
  end

  def get_promotion_discount(order, item)
    return 0.0 if (promotion = order.data[:promotion]).blank?

    (promotion[:discount].to_f / order.total_products) * item.quantity
  end

  def get_paid_shipping_price(item)
    item.shipment_cost
  end

  def get_iva(product)
    product.iva || '-'
  end

  def get_subsidized_shipping(suborder, item, order) # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity
    if suborder.fulfilled_by_gp && order.shipments.count > 1 && order.shipments_cost.positive?
      return (suborder.shipment_cost.to_f / suborder.order.total_products) * item.quantity
    end
    return 0.0 unless suborder.has_shipment_bonification?
    return 0.0 if suborder.shipment.nil?

    begin
      (suborder.shipment.bonified_amount.to_f / suborder.total_products) * item.quantity
    rescue StandardError
      0.0
    end
  end

  def get_label_from_shipment(suborder)
    suborder.shipment&.labels&.reverse&.find { |lbl| lbl.cancelled_at.nil? }
  end

  def get_label_courier(suborder)
    label = get_label_from_shipment(suborder)
    label&.try(:courier) || '-'
  end

  def get_label_tracking_number(suborder)
    label = get_label_from_shipment(suborder)
    sanitize(label&.try(:tracking_number) || '-')
  end

  def get_telephone(suborder, order)
    telephone = order.customer.telephone if order.customer.present?

    unless suborder.shipment.present? && suborder.shipment.destination_address.present?
      return telephone || '-'
    end

    suborder.shipment.destination_address.telephone
  rescue StandardError
    '-'
  end

  def get_label_amount(suborder, item) # rubocop:disable Metrics/AbcSize
    label = get_label_from_shipment(suborder)

    if  label.present? &&
        label[:gateway_data].present? &&
        label[:gateway_data][:rate].present?

      (label[:gateway_data][:rate][:precio].to_f / suborder.total_products) * item.quantity
    else
      '-'
    end
  end

  def get_label_paid_by(suborder, network) # rubocop:disable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
    return '-' if suborder.shipment.nil?

    shipment = suborder.shipment
    label = get_label_from_shipment(suborder)
    if shipment.status == 'unfulfilled'
      '-'
    elsif (label.present? && label[:gateway].present?) \
                || (network == 'AR' && shipment[:gateway].present?)
      'Avenida Store'
    else
      'Shop'
    end
  end

  def get_goodpeople_commission(order_item)
    order_item.commission.to_f
  rescue StandardError
    0.0
  end

  def get_shop_commission(suborder)
    return suborder.shop_commission.to_f if suborder.shop_commission.present?

    suborder.shop.setting.commercial_agreement[:sale_commission].to_f
  end

  def get_destination_address_fullname(suborder)
    return '-' if suborder.shipment.nil?

    begin
      suborder.shipment.destination_address.full_name
    rescue StandardError
      '-'
    end
  end

  def get_label_date(suborder)
    label = get_label_from_shipment(suborder)
    return date_format(label.created_at) if label&.created_at.present?

    '-'
  end

  def get_invoice_id(suborder)
    if (invoices = suborder.order.invoices).present? && !suborder.shop.fc?
      invoices.first.gateway_data[:number]
    else
      '-'
    end
  end

  def get_id_cobis(customer)
    customer.try(:uuid) || '-'
  end

  def get_total_consumed_points(order)
    order&.try(:total_points) || '-'
  end

  def item_total_consumed_points(item)
    item.points || '-'
  end

  def get_points_relation(item)
    item&.try(:point_equivalent) || '-'
  end

  def get_points_money_relation(item)
    item&.points_money || '-'
  end

  def get_cancelation_reason(suborder)
    return "-" if suborder.nil? || suborder.data.nil? || suborder.data.empty?
    suborder.data.dig(:cancel, :reason) || "-"
  end

  def get_order_comment(order)
    return "-" if order.nil? || order.comment.nil? || order.comment.empty?
    order.comment || '-'
  end

  #PAYMENTS

  def get_payment_external_id(payment)
    payment&.get_external_id || '-'
  end

  def item_payment_external_ids(payment)
    payment&.gateway_object_id || '-'
  end

  def payment_method(payment)
    payment&.payment_method || '-'
  end

  def item_payment_methods(payment)
    payment&.payment_method_name || '-'
  end

  def payment_total(payment)
    payment&.collected_amount.to_i || '-'
  end

  def suborder_payment_total(suborder, _gateway)
    suborder.total
  end

  def get_credit_card(payment)
    payment&.get_cc_brand || '-'
  end

  def get_cc_number(payment)
    payment&.get_cc_bin || '-'
  end

  def get_installments(payment)
    payment&.get_installments || '-'
  end

  def card_last_four_digits(payment)
    payment&.card_last_four_digits || '-'
  end

  def item_cc_bank(payment)
    return "CIUDAD" if payment&.gateway&.downcase == 'firstdata' || payment&.gateway == 'FirstDataDistributed'
    payment&.get_cc_bank || '-'
  end

  def get_identification(payment, key)
    return '-' if payment.blank?

    # Estándar
    identification = payment&.gateway_data&.dig(:payer, :identification)
    if identification.present?
      return identification[:name_payment] if key == :name_payment && identification[:name_payment].present?
      return identification[:doc_type] if key == :type && identification[:doc_type].present?
      return identification[:type] if key == :type && identification[:type].present?
      return identification[:doc_number] if key == :number && identification[:doc_number].present?
      return identification[:number] if key == :number && identification[:number].present?
    end

    # Manejo por gateway
    case payment.gateway
    when 'AvenidaModoDistributed', 'AvenidaModo'
      return handle_avenida_modo(payment, key)
    when 'FirstDataDistributed', 'FirstData'
      return handle_first_data(payment, key)
    end

    '-'
  end

  def handle_avenida_modo(payment, key)
    payer_data = payment&.gateway_data&.dig(:request, :payer)
    case key
    when :name_payment
      return payer_data[:name] if payer_data&.key?(:name)
    when :type
      return payment.document_type if payment.document_type.present?
      return payer_data[:doc_type] if payer_data&.key?(:doc_type)
    when :number
      return payment.document_number if payment.document_number.present?
      return payer_data[:doc_number] if payer_data&.key?(:doc_number)
    end
    '-'
  end

  def handle_first_data(payment, key)
    case key
    when :name_payment
      return payment.card_holder_name
    when :type
      return payment.document_type
    when :number
      return payment.document_number
    end
    '-'
  end

  def get_purchase_id_for_export(payment, suborder)
    # Solo para pagos de puntos (LoyaltyBna)
    return "-" unless payment&.gateway == "LoyaltyBna"

    # Intentar obtener el purchase_id de diferentes formas según el contexto
    purchase_id = nil

    if suborder.present?
      # Para suborders, usar get_subpayment_site_id
      purchase_id = payment.get_subpayment_site_id(suborder, suborder.shop)&.dig('points_uuid')
    else
      # Para purchases u órdenes directas, buscar en gateway_data
      purchase_id = payment.gateway_data&.dig('points_uuid')
    end

    return "-" unless purchase_id.present?

    # Formatear como en la vista: quitar guiones y tomar primeros 20 caracteres
    purchase_id.to_s.gsub('-', '')[0..19]
  end
end
