require File.expand_path('../../../spec_helper.rb', __FILE__)

module Pioneer
  describe OrderExporter do
    let(:network){ 'US' }
    let(:orders){ create_list(:order, 2, :with_suborders, :with_payment, :with_shipment, network: network) }

    describe 'csv upstream exporter' do
      let(:order)    { orders.sample }
      let(:item)     { order.items.sample }
      let(:suborder) { item.suborder }

      before do
        @exporter = OrderExporter.new({network: network, orders: orders})
        @stream = @exporter.stream
      end

      it do
        expect(@stream.first).to eq(@exporter.send(:csv_header))
        expect(@stream.count).to eq(orders.flat_map(&:items).count + 1)
        expect(@stream).to include(@exporter.send(:csv_row, @exporter.send(:row_data, item, suborder, order)))
      end
    end

    describe 'Purchase ID functionality' do
      let(:network) { 'AR' }
      let(:exporter) { OrderExporter.new({network: network, orders: []}) }

      describe '#get_purchase_id' do
        let(:suborder) { double('suborder') }
        let(:shop) { double('shop') }
        let(:order) { double('order') }

        before do
          allow(suborder).to receive(:shop).and_return(shop)
          allow(suborder).to receive(:order).and_return(order)
        end

        context 'when there is a LoyaltyBna payment with points_uuid' do
          let(:points_payment) do
            double('payment',
              gateway: 'LoyaltyBna',
              get_subpayment_site_id: { 'points_uuid' => 'abc123-def456-ghi789-jkl012' }
            )
          end

          before do
            allow(suborder).to receive(:payments).and_return([])
            allow(order).to receive(:payments).and_return([points_payment])
          end

          it 'returns formatted purchase ID' do
            result = exporter.send(:get_purchase_id, suborder)
            expect(result).to eq('abc123def456ghi789jk')
          end
        end

        context 'when there is no LoyaltyBna payment' do
          before do
            allow(suborder).to receive(:payments).and_return([])
            allow(order).to receive(:payments).and_return([])
          end

          it 'returns dash' do
            result = exporter.send(:get_purchase_id, suborder)
            expect(result).to eq('-')
          end
        end

        context 'when LoyaltyBna payment exists but no points_uuid' do
          let(:points_payment) do
            double('payment',
              gateway: 'LoyaltyBna',
              get_subpayment_site_id: {}
            )
          end

          before do
            allow(suborder).to receive(:payments).and_return([])
            allow(order).to receive(:payments).and_return([points_payment])
          end

          it 'returns dash' do
            result = exporter.send(:get_purchase_id, suborder)
            expect(result).to eq('-')
          end
        end
      end

      describe '#get_purchase_id_for_purchase' do
        let(:order) { double('order') }

        context 'when there is a LoyaltyBna payment with points_uuid in gateway_data' do
          let(:points_payment) do
            double('payment',
              gateway: 'LoyaltyBna',
              gateway_data: { 'points_uuid' => 'xyz789-abc123-def456-ghi012' }
            )
          end

          before do
            allow(order).to receive(:payments).and_return([points_payment])
          end

          it 'returns formatted purchase ID' do
            result = exporter.send(:get_purchase_id_for_purchase, order)
            expect(result).to eq('xyz789abc123def456gh')
          end
        end

        context 'when there is no LoyaltyBna payment' do
          before do
            allow(order).to receive(:payments).and_return([])
          end

          it 'returns dash' do
            result = exporter.send(:get_purchase_id_for_purchase, order)
            expect(result).to eq('-')
          end
        end
      end
    end
  end
end
